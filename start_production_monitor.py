#!/usr/bin/env python3
"""
生产环境监控系统启动脚本
Production Monitor Startup Script
"""

import sys
import time
import datetime
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

try:
    from web.utils.monitor_integration import monitor_integration
except ImportError as e:
    print(f"导入失败: {e}")
    sys.exit(1)

def main():
    print("=== 生产环境监控系统启动 ===")
    print(f"时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n1. 启动监控系统...")
    result = monitor_integration.start_monitor()
    print(f"   启动结果: {result.get('success', False)}")
    print(f"   消息: {result.get('message', '无消息')}")
    
    if result.get('success'):
        print("\n2. 等待系统初始化...")
        for i in range(8):
            time.sleep(1)
            print(f"   初始化中... {i+1}/8")
        
        print("\n3. 检查系统运行状态...")
        status = monitor_integration.get_system_status()
        print(f"   监控状态: {status.get('monitor_running', False)}")
        print(f"   监控币种: {status.get('monitored_symbols', 0)}")
        print(f"   总告警数: {status.get('total_alerts', 0)}")
        print(f"   24h信号: {status.get('recent_signals', 0)}")
        
        if status.get('next_kline_time'):
            print(f"   下个K线: {status['next_kline_time']}")
        
        print("\n✅ 监控系统已在生产环境中运行")
        print("📊 Web界面地址: http://localhost:8501")
        print("🔍 策略监控页面: 点击'策略监控'按钮")
        
        # 显示系统信息
        print("\n📋 系统信息:")
        print(f"   - 数据库: {project_root}/data/usdt_historical_data.db")
        print(f"   - 告警库: {project_root}/data/alerts.db")
        print(f"   - 配置: {project_root}/config/")
        print(f"   - 日志: 实时显示在Web界面")
        
    else:
        print("\n❌ 监控系统启动失败")
        return False
    
    print("\n=== 生产部署完成 ===")
    return True

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 系统已成功部署到生产环境！")
        print("请访问 http://localhost:8501 查看Web界面")
    else:
        print("\n💥 部署失败，请检查错误信息")
        sys.exit(1)
