# Playwright MCP 安装指南

## 环境检查 ✅
- Node.js: v24.1.0 (满足要求 ≥18)
- NPM: v11.3.0

## 安装方法

### 方法1：Claude Desktop 配置 (推荐)

1. **找到Claude Desktop配置文件**
   - Windows: `%APPDATA%\Claude\claude_desktop_config.json`
   - 如果文件不存在，创建一个新文件

2. **添加配置**
   ```json
   {
     "mcpServers": {
       "playwright": {
         "command": "npx",
         "args": [
           "@playwright/mcp@latest"
         ]
       }
     }
   }
   ```

3. **重启Claude Desktop**
   - 完全关闭Claude <PERSON>
   - 重新启动应用

### 方法2：测试安装

1. **预安装Playwright MCP**
   ```bash
   npx @playwright/mcp@latest --help
   ```

2. **安装浏览器**
   ```bash
   npx @playwright/mcp@latest --install
   ```

## 配置选项

### 基础配置
```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest",
        "--headless",
        "--browser", "chrome"
      ]
    }
  }
}
```

### 高级配置
```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest",
        "--headless",
        "--browser", "chrome",
        "--viewport-size", "1280,720",
        "--user-agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "--ignore-https-errors",
        "--no-sandbox"
      ]
    }
  }
}
```

## 验证安装

安装完成后，在Claude Desktop中应该能看到以下工具：

### 核心工具
- `browser_snapshot` - 页面快照
- `browser_navigate` - 页面导航
- `browser_click` - 点击操作
- `browser_type` - 文本输入
- `browser_take_screenshot` - 截图

### 测试命令
在Claude Desktop中输入：
```
请帮我打开 http://localhost:8501 并截图
```

## 故障排除

### 常见问题
1. **Node.js版本过低**
   - 确保Node.js ≥ 18
   - 升级：下载最新版本从 nodejs.org

2. **权限问题**
   - 以管理员身份运行命令
   - 检查防火墙设置

3. **浏览器安装失败**
   ```bash
   npx playwright install chromium
   ```

4. **配置文件路径错误**
   - 确认配置文件位置正确
   - 检查JSON语法

### 调试模式
```json
{
  "mcpServers": {
    "playwright": {
      "command": "npx",
      "args": [
        "@playwright/mcp@latest",
        "--port", "8931"
      ]
    }
  }
}
```

## 使用示例

安装成功后，您可以要求我：
1. 打开TBTrade Web界面并截图
2. 自动填写表单
3. 点击按钮和链接
4. 检查页面元素
5. 生成页面报告

## 下一步

安装完成后，我就能：
- 直接查看您的Streamlit应用
- 截图展示页面状态
- 自动测试Web功能
- 调试界面问题
