#!/usr/bin/env python3
"""
语法检查脚本
"""

import ast

def check_syntax(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 尝试编译
        ast.parse(code, filename)
        print(f"✅ {filename} 语法正确")
        return True
        
    except SyntaxError as e:
        print(f"❌ {filename} 语法错误:")
        print(f"   行 {e.lineno}: {e.text.strip() if e.text else 'N/A'}")
        print(f"   错误: {e.msg}")
        
        # 显示错误行周围的代码
        lines = code.split('\n')
        start = max(0, e.lineno - 3)
        end = min(len(lines), e.lineno + 2)
        
        print("\n   上下文:")
        for i in range(start, end):
            marker = " >>> " if i + 1 == e.lineno else "     "
            print(f"{marker}{i+1:3d}: {lines[i]}")
        
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

if __name__ == "__main__":
    check_syntax("web/pages/01_📊_回测分析.py")
