#!/usr/bin/env python3
"""
监控系统集成模块
Monitor System Integration Module

提供Web界面与auto_4h_monitor系统的集成接口
"""

import asyncio
import threading
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import json
import logging
import sys

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

try:
    from tools.auto_4h_monitor import Auto4HMonitor
    from src.data_layer.historical_data_fetcher import scan_local_databases
except ImportError as e:
    print(f"导入监控模块失败: {e}")

class MonitorIntegration:
    """监控系统集成类"""
    
    def __init__(self):
        """初始化监控集成"""
        self.monitor_instance = None
        self.monitor_thread = None
        self.is_running = False
        self.data_dir = project_root / "data"
        self.alerts_db = self.data_dir / "alerts.db"
        
        # 确保数据目录存在
        self.data_dir.mkdir(exist_ok=True)
        
        # 初始化告警数据库
        self._init_alerts_db()
    
    def _init_alerts_db(self):
        """初始化告警数据库"""
        try:
            conn = sqlite3.connect(str(self.alerts_db))
            conn.execute("""
                CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    symbol TEXT NOT NULL,
                    strategy TEXT NOT NULL,
                    signal_type TEXT NOT NULL,
                    confidence REAL NOT NULL,
                    price REAL NOT NULL,
                    reason TEXT,
                    indicators TEXT,
                    notification_status TEXT,
                    execution_status TEXT
                )
            """)
            
            # 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_timestamp ON alerts(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_symbol ON alerts(symbol)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_strategy ON alerts(strategy)")
            
            conn.commit()
            conn.close()
        except Exception as e:
            print(f"初始化告警数据库失败: {e}")
    
    def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            status = {
                'monitor_running': self.is_running,
                'last_update': datetime.now().isoformat(),
                'monitored_symbols': 0,
                'total_alerts': 0,
                'recent_signals': 0,
                'next_kline_time': None,
                'current_kline_time': None
            }
            
            # 获取监控币种数量
            databases = scan_local_databases(str(self.data_dir))
            if databases:
                status['monitored_symbols'] = databases[0].get('unique_symbols', 0)
            
            # 获取告警统计
            alerts_stats = self.get_alerts_statistics()
            status.update(alerts_stats)
            
            # 获取K线时间信息
            if self.monitor_instance:
                try:
                    current_kline = self.monitor_instance._get_current_4h_kline_time()
                    next_kline = self.monitor_instance._get_next_4h_kline_time()
                    status['current_kline_time'] = current_kline.isoformat()
                    status['next_kline_time'] = next_kline.isoformat()
                except:
                    pass
            
            return status
        except Exception as e:
            return {'error': str(e)}
    
    def get_alerts_statistics(self) -> Dict[str, Any]:
        """获取告警统计信息"""
        try:
            if not self.alerts_db.exists():
                return {'total_alerts': 0, 'recent_signals': 0}
            
            conn = sqlite3.connect(str(self.alerts_db))
            
            # 总告警数
            cursor = conn.execute("SELECT COUNT(*) FROM alerts")
            total_alerts = cursor.fetchone()[0]
            
            # 最近24小时信号
            recent_time = (datetime.now() - timedelta(hours=24)).isoformat()
            cursor = conn.execute("SELECT COUNT(*) FROM alerts WHERE timestamp > ?", (recent_time,))
            recent_signals = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'total_alerts': total_alerts,
                'recent_signals': recent_signals
            }
        except Exception as e:
            return {'total_alerts': 0, 'recent_signals': 0, 'error': str(e)}
    
    def get_alerts_data(self, limit: int = 100, 
                       symbol_filter: List[str] = None,
                       strategy_filter: List[str] = None,
                       signal_filter: List[str] = None) -> pd.DataFrame:
        """获取告警数据"""
        try:
            if not self.alerts_db.exists():
                return pd.DataFrame()
            
            conn = sqlite3.connect(str(self.alerts_db))
            
            # 构建查询
            query = "SELECT * FROM alerts"
            params = []
            conditions = []
            
            if symbol_filter:
                placeholders = ','.join(['?' for _ in symbol_filter])
                conditions.append(f"symbol IN ({placeholders})")
                params.extend(symbol_filter)
            
            if strategy_filter:
                placeholders = ','.join(['?' for _ in strategy_filter])
                conditions.append(f"strategy IN ({placeholders})")
                params.extend(strategy_filter)
            
            if signal_filter:
                placeholders = ','.join(['?' for _ in signal_filter])
                conditions.append(f"signal_type IN ({placeholders})")
                params.extend(signal_filter)
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            query += " ORDER BY timestamp DESC"
            
            if limit:
                query += f" LIMIT {limit}"
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            if not df.empty:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
            
            return df
        except Exception as e:
            print(f"获取告警数据失败: {e}")
            return pd.DataFrame()
    
    def start_monitor(self, config: Dict[str, Any] = None) -> Dict[str, Any]:
        """启动监控系统"""
        try:
            if self.is_running:
                return {'success': False, 'message': '监控系统已在运行中'}
            
            # 创建监控实例
            self.monitor_instance = Auto4HMonitor()
            
            # 在新线程中运行监控系统
            def run_monitor():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.monitor_instance.start())
                except Exception as e:
                    print(f"监控系统运行错误: {e}")
                    self.is_running = False
            
            self.monitor_thread = threading.Thread(target=run_monitor, daemon=True)
            self.monitor_thread.start()
            self.is_running = True
            
            return {
                'success': True,
                'message': '监控系统启动成功',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'启动失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def stop_monitor(self) -> Dict[str, Any]:
        """停止监控系统"""
        try:
            if not self.is_running:
                return {'success': False, 'message': '监控系统未运行'}
            
            self.is_running = False
            
            if self.monitor_instance:
                # 异步停止监控系统
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.monitor_instance.stop())
                except:
                    pass
                
                self.monitor_instance = None
            
            return {
                'success': True,
                'message': '监控系统已停止',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'停止失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_monitor_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        try:
            config = {
                'monitor_interval': '4h',
                'max_symbols': 50,
                'strategies': ['EMABreakout'],
                'data_source': 'binance',
                'notification_enabled': True
            }
            return config
        except Exception as e:
            return {'error': str(e)}
    
    def update_monitor_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """更新监控配置"""
        try:
            # 这里可以实现配置更新逻辑
            # 目前返回成功状态
            return {
                'success': True,
                'message': '配置更新成功',
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'success': False,
                'message': f'配置更新失败: {str(e)}',
                'timestamp': datetime.now().isoformat()
            }
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据概览"""
        try:
            databases = scan_local_databases(str(self.data_dir))
            
            if not databases:
                return {'error': '未找到数据库'}
            
            summary = {
                'databases': len(databases),
                'total_symbols': 0,
                'total_klines': 0,
                'date_range': {},
                'intervals': []
            }
            
            for db_info in databases:
                summary['total_symbols'] += db_info.get('unique_symbols', 0)
                summary['total_klines'] += db_info.get('total_klines', 0)
                
                if 'earliest_date' in db_info and 'latest_date' in db_info:
                    summary['date_range'] = {
                        'start': db_info['earliest_date'],
                        'end': db_info['latest_date']
                    }
                
                if 'interval' in db_info:
                    summary['intervals'].append(db_info['interval'])
            
            summary['intervals'] = list(set(summary['intervals']))
            
            return summary
        except Exception as e:
            return {'error': str(e)}

# 创建全局集成实例
monitor_integration = MonitorIntegration()
