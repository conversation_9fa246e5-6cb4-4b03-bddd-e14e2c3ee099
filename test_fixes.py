#!/usr/bin/env python3
"""
测试修复结果
"""

print("=== 测试修复结果 ===")

# 测试语法
print("1. 测试回测分析页面语法...")
try:
    with open("pages/01_📊_回测分析.py", "r", encoding="utf-8") as f:
        code = f.read()
    compile(code, "pages/01_📊_回测分析.py", "exec")
    print("   ✅ 语法检查通过")
except SyntaxError as e:
    print(f"   ❌ 语法错误: {e}")

# 测试导入
print("2. 测试组件导入...")
try:
    import sys
    sys.path.append("..")
    from web.components.charts import create_line_chart, create_bar_chart
    from web.components.tables import create_data_table
    print("   ✅ 组件导入成功")
except ImportError as e:
    print(f"   ❌ 导入失败: {e}")

print("3. 测试监控集成...")
try:
    from utils.monitor_integration import monitor_integration
    print("   ✅ 监控集成导入成功")
except ImportError as e:
    print(f"   ❌ 监控集成导入失败: {e}")

print("\n✅ 修复测试完成")
